#!/usr/bin/env python3
"""
Primer/Probe Design Pipeline for Conserved Regions
Based on ppdesign methodology but simplified for direct execution

This script designs 20bp primers/probes from conserved regions in multiple sequence alignments.
"""

import os
import sys
import argparse
from pathlib import Path
from collections import defaultdict, Counter
import logging
from typing import Dict, List, Tuple, Set
import tempfile
import subprocess

# Import required packages
try:
    from Bio import SeqIO, Align
    from Bio.Seq import Seq
    from Bio.SeqRecord import SeqRecord
    import pandas as pd
    import numpy as np
    from tqdm import tqdm
    import primer3
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install: pip install biopython pandas numpy tqdm primer3-py")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConservedRegion:
    """Represents a conserved region in multiple sequences."""
    
    def __init__(self, start: int, end: int, consensus: str, conservation_score: float):
        self.start = start
        self.end = end
        self.consensus = consensus
        self.conservation_score = conservation_score
        self.length = end - start + 1
    
    def __repr__(self):
        return f"ConservedRegion({self.start}-{self.end}, score={self.conservation_score:.3f}, seq={self.consensus[:20]}...)"

class KmerBasedFinder:
    """Find conserved regions using k-mer analysis."""
    
    def __init__(self, kmer_size: int = 20, min_conservation: float = 0.8):
        self.kmer_size = kmer_size
        self.min_conservation = min_conservation
    
    def find_conserved_regions(self, sequences: List[SeqRecord]) -> List[ConservedRegion]:
        """Find conserved regions using k-mer frequency analysis."""
        logger.info(f"Finding conserved regions using {self.kmer_size}-mers with min conservation {self.min_conservation}")
        
        if len(sequences) < 2:
            logger.warning("Need at least 2 sequences for conservation analysis")
            return []
        
        # Extract k-mers from all sequences
        kmer_positions = defaultdict(list)
        sequence_kmers = {}
        
        for i, record in enumerate(sequences):
            seq_str = str(record.seq).upper()
            sequence_kmers[i] = set()
            
            for j in range(len(seq_str) - self.kmer_size + 1):
                kmer = seq_str[j:j + self.kmer_size]
                if 'N' not in kmer:  # Skip ambiguous sequences
                    kmer_positions[kmer].append((i, j))
                    sequence_kmers[i].add(kmer)
        
        # Find conserved k-mers
        conserved_regions = []
        total_sequences = len(sequences)
        
        for kmer, positions in kmer_positions.items():
            # Calculate conservation score
            sequence_ids = set(pos[0] for pos in positions)
            conservation_score = len(sequence_ids) / total_sequences
            
            if conservation_score >= self.min_conservation:
                # Use the first occurrence position as representative
                first_pos = min(pos[1] for pos in positions if pos[0] == 0) if 0 in sequence_ids else positions[0][1]
                
                region = ConservedRegion(
                    start=first_pos,
                    end=first_pos + self.kmer_size - 1,
                    consensus=kmer,
                    conservation_score=conservation_score
                )
                conserved_regions.append(region)
        
        # Sort by conservation score and position
        conserved_regions.sort(key=lambda x: (-x.conservation_score, x.start))
        
        logger.info(f"Found {len(conserved_regions)} conserved regions")
        return conserved_regions

class PrimerDesigner:
    """Design primers/probes from conserved regions."""
    
    def __init__(self, target_length: int = 20, gc_range: Tuple[float, float] = (40, 60), 
                 tm_range: Tuple[float, float] = (55, 65)):
        self.target_length = target_length
        self.gc_range = gc_range
        self.tm_range = tm_range
    
    def calculate_gc_content(self, sequence: str) -> float:
        """Calculate GC content percentage."""
        gc_count = sequence.count('G') + sequence.count('C')
        return (gc_count / len(sequence)) * 100 if len(sequence) > 0 else 0
    
    def calculate_tm(self, sequence: str) -> float:
        """Calculate melting temperature using primer3."""
        try:
            return primer3.calc_tm(sequence)
        except:
            # Fallback simple calculation
            return 64.9 + 41 * (sequence.count('G') + sequence.count('C') - 16.4) / len(sequence)
    
    def design_primers(self, conserved_regions: List[ConservedRegion]) -> List[Dict]:
        """Design primers from conserved regions."""
        logger.info(f"Designing primers with target length {self.target_length}bp")
        
        primers = []
        
        for i, region in enumerate(conserved_regions):
            sequence = region.consensus
            
            # If region is longer than target, extract windows
            if len(sequence) >= self.target_length:
                for start in range(len(sequence) - self.target_length + 1):
                    primer_seq = sequence[start:start + self.target_length]
                    
                    # Calculate properties
                    gc_content = self.calculate_gc_content(primer_seq)
                    tm = self.calculate_tm(primer_seq)
                    
                    # Check if meets criteria
                    if (self.gc_range[0] <= gc_content <= self.gc_range[1] and 
                        self.tm_range[0] <= tm <= self.tm_range[1]):
                        
                        primer = {
                            'primer_id': f"primer_{i+1}_{start+1}",
                            'sequence': primer_seq,
                            'length': len(primer_seq),
                            'gc_content': gc_content,
                            'tm': tm,
                            'conservation_score': region.conservation_score,
                            'region_start': region.start + start,
                            'region_end': region.start + start + self.target_length - 1,
                            'source_region': f"region_{i+1}"
                        }
                        primers.append(primer)
            
            elif len(sequence) >= 15:  # Allow slightly shorter primers
                gc_content = self.calculate_gc_content(sequence)
                tm = self.calculate_tm(sequence)
                
                primer = {
                    'primer_id': f"primer_{i+1}",
                    'sequence': sequence,
                    'length': len(sequence),
                    'gc_content': gc_content,
                    'tm': tm,
                    'conservation_score': region.conservation_score,
                    'region_start': region.start,
                    'region_end': region.end,
                    'source_region': f"region_{i+1}"
                }
                primers.append(primer)
        
        # Sort by conservation score and thermodynamic properties
        primers.sort(key=lambda x: (-x['conservation_score'], abs(x['tm'] - 60), abs(x['gc_content'] - 50)))
        
        logger.info(f"Designed {len(primers)} primers meeting criteria")
        return primers

def load_sequences(fasta_file: str) -> List[SeqRecord]:
    """Load sequences from FASTA file."""
    logger.info(f"Loading sequences from {fasta_file}")
    
    sequences = []
    try:
        for record in SeqIO.parse(fasta_file, "fasta"):
            sequences.append(record)
        
        logger.info(f"Loaded {len(sequences)} sequences")
        return sequences
    
    except Exception as e:
        logger.error(f"Error loading sequences: {e}")
        return []

def save_results(primers: List[Dict], output_file: str):
    """Save primer design results to CSV."""
    logger.info(f"Saving results to {output_file}")
    
    if not primers:
        logger.warning("No primers to save")
        return
    
    df = pd.DataFrame(primers)
    df.to_csv(output_file, index=False)
    
    # Print summary
    print(f"\n=== PRIMER DESIGN RESULTS ===")
    print(f"Total primers designed: {len(primers)}")
    print(f"Results saved to: {output_file}")
    
    if len(primers) > 0:
        print(f"\nTop 10 primers:")
        print(df[['primer_id', 'sequence', 'length', 'gc_content', 'tm', 'conservation_score']].head(10).to_string(index=False))

def main():
    parser = argparse.ArgumentParser(description="Design primers/probes from conserved regions")
    parser.add_argument("input_fasta", help="Input FASTA file with multiple sequences")
    parser.add_argument("-o", "--output", default="primer_results.csv", help="Output CSV file")
    parser.add_argument("-k", "--kmer-size", type=int, default=20, help="K-mer size for conservation analysis")
    parser.add_argument("-c", "--conservation", type=float, default=0.8, help="Minimum conservation score (0-1)")
    parser.add_argument("-l", "--length", type=int, default=20, help="Target primer length")
    parser.add_argument("--gc-min", type=float, default=40, help="Minimum GC content (%)")
    parser.add_argument("--gc-max", type=float, default=60, help="Maximum GC content (%)")
    parser.add_argument("--tm-min", type=float, default=55, help="Minimum melting temperature (°C)")
    parser.add_argument("--tm-max", type=float, default=65, help="Maximum melting temperature (°C)")
    
    args = parser.parse_args()
    
    # Load sequences
    sequences = load_sequences(args.input_fasta)
    if not sequences:
        logger.error("No sequences loaded. Exiting.")
        return 1
    
    # Find conserved regions
    finder = KmerBasedFinder(kmer_size=args.kmer_size, min_conservation=args.conservation)
    conserved_regions = finder.find_conserved_regions(sequences)
    
    if not conserved_regions:
        logger.error("No conserved regions found. Try lowering conservation threshold.")
        return 1
    
    # Design primers
    designer = PrimerDesigner(
        target_length=args.length,
        gc_range=(args.gc_min, args.gc_max),
        tm_range=(args.tm_min, args.tm_max)
    )
    primers = designer.design_primers(conserved_regions)
    
    # Save results
    save_results(primers, args.output)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
