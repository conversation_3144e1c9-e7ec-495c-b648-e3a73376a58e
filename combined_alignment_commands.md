# Combined BWA-MEM2 Alignment Commands Reference
## Merging Two Read Files into Single BAM for Maximum Coverage

## Quick Start - Combined Alignment
```bash
# Navigate to working directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV

# Run the complete combined alignment pipeline
./align_combined_reads.sh
```

## Individual Commands for Manual Execution

### 1. Setup and Environment Variables
```bash
# Set all required paths
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M"
PIXI_ENV_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"

# Define read files
READS1="${READS_DIR}/FSEM002C_S40_L007.anqdpht.fastq.gz"
READS2="${READS_DIR}/FSEM001O_S20_L008.anqdpht.fastq.gz"

# Output file names
COMBINED_SAM="FSEM002C_001O_combined_aligned.sam"
COMBINED_BAM="FSEM002C_001O_combined_aligned.bam"
SORTED_BAM="FSEM002C_001O_combined_aligned.sorted.bam"
```

### 2. Verify Input Files
```bash
# Check file existence and sizes
ls -lh $GENOME
ls -lh $READS1
ls -lh $READS2

# Check total read data size
du -ch $READS1 $READS2

# Verify BWA-MEM2 executable
$BWA_MEM2 version
```

### 3. Combined BWA-MEM2 Alignment (KEY STEP)
```bash
# Align BOTH read files together into a SINGLE SAM file
# This maximizes mapped reads by combining all data
$BWA_MEM2 mem $GENOME $READS1 $READS2 > $COMBINED_SAM

# Alternative with threading (if you want to use multiple cores)
$BWA_MEM2 mem -t 4 $GENOME $READS1 $READS2 > $COMBINED_SAM
```

### 4. Convert Combined SAM to BAM
```bash
# Navigate to pixi environment
cd $PIXI_ENV_DIR

# Convert SAM to BAM
pixi run samtools view -bS ../$COMBINED_SAM > ../$COMBINED_BAM

# Alternative with compression level
pixi run samtools view -bS -@ 4 ../$COMBINED_SAM > ../$COMBINED_BAM
```

### 5. Sort Combined BAM
```bash
# Sort the combined BAM file
pixi run samtools sort ../$COMBINED_BAM -o ../$SORTED_BAM

# Alternative with memory specification
pixi run samtools sort -m 2G ../$COMBINED_BAM -o ../$SORTED_BAM
```

### 6. Index Combined BAM for IGV
```bash
# Create index for IGV viewing
pixi run samtools index ../$SORTED_BAM

# This creates: FSEM002C_001O_combined_aligned.sorted.bam.bai
```

### 7. Generate Comprehensive Statistics
```bash
# Basic alignment statistics
pixi run samtools flagstat ../$SORTED_BAM

# Detailed coverage analysis
pixi run samtools coverage ../$SORTED_BAM

# Count reads
TOTAL_READS=$(pixi run samtools view -c ../$SORTED_BAM)
MAPPED_READS=$(pixi run samtools view -c -F 4 ../$SORTED_BAM)
UNMAPPED_READS=$(pixi run samtools view -c -f 4 ../$SORTED_BAM)

echo "Total reads: $TOTAL_READS"
echo "Mapped reads: $MAPPED_READS"
echo "Unmapped reads: $UNMAPPED_READS"
```

### 8. Coverage Depth Analysis
```bash
# Generate detailed coverage depth file
pixi run samtools depth ../$SORTED_BAM > ../combined_coverage_depth.txt

# Generate coverage histogram
pixi run samtools depth ../$SORTED_BAM | awk '{print $3}' | sort -n | uniq -c > ../coverage_histogram.txt

# Calculate average coverage
pixi run samtools depth ../$SORTED_BAM | awk '{sum+=$3} END {print "Average coverage:", sum/NR}'
```

### 9. Quality Control Checks
```bash
# Check BAM file integrity
pixi run samtools quickcheck ../$SORTED_BAM

# View sample alignments
pixi run samtools view ../$SORTED_BAM | head -10

# Check mapping quality distribution
pixi run samtools view ../$SORTED_BAM | awk '{print $5}' | sort -n | uniq -c
```

## IGV Viewing Instructions

### Loading Combined BAM in IGV:
1. **Load Reference Genome**:
   ```
   File → Load Genome → Browse to:
   /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna
   ```

2. **Load Combined BAM File**:
   ```
   File → Load from File → Select:
   FSEM002C_001O_combined_aligned.sorted.bam
   ```
   - The `.bai` index file will be automatically detected
   - This single BAM contains reads from BOTH input files

3. **Optimize IGV Display**:
   - Right-click on track → "Color alignments by" → "read strand"
   - Right-click on track → "Group alignments by" → "sample"
   - Adjust coverage track height for better visualization

## Advantages of Combined Approach

### Why Combine Both Read Files:
1. **Maximum Coverage**: All reads from both files contribute to coverage
2. **Better Variant Detection**: More reads = better statistical power
3. **Simplified Analysis**: Single BAM file instead of multiple files
4. **Increased Mapping Success**: Reads that might not map individually may map when combined

### Expected Results:
- **Input**: 1.7GB + 1.0GB = 2.7GB total read data
- **Output**: Single sorted BAM (~3-6GB) with maximum possible mapped reads
- **Coverage**: Combined coverage from both samples on reference genome

## File Management Commands

### Monitor Progress:
```bash
# Check alignment progress
ps aux | grep bwa-mem2

# Monitor file sizes during processing
watch -n 30 'ls -lh *.sam *.bam 2>/dev/null'

# Check disk space
df -h /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV
```

### Cleanup Commands (Optional):
```bash
# Remove large intermediate files to save space
rm $COMBINED_SAM      # Remove large SAM file (can be recreated from BAM)
rm $COMBINED_BAM      # Remove unsorted BAM (sorted version exists)

# Keep only essential files:
# - FSEM002C_001O_combined_aligned.sorted.bam
# - FSEM002C_001O_combined_aligned.sorted.bam.bai
# - MS2_N4.fna (reference)
```

## Troubleshooting

### Common Issues:
```bash
# If alignment fails, check:
$BWA_MEM2 mem $GENOME $READS1 $READS2 2>&1 | head -20

# If samtools fails, check pixi environment:
cd $PIXI_ENV_DIR && pixi run samtools --version

# If out of disk space:
df -h /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV
```

### Performance Optimization:
```bash
# Use multiple threads for faster processing:
$BWA_MEM2 mem -t 8 $GENOME $READS1 $READS2 > $COMBINED_SAM

# Use more memory for sorting:
pixi run samtools sort -m 4G -@ 4 ../$COMBINED_BAM -o ../$SORTED_BAM
```

## Expected Timeline
- **Combined Alignment**: 20-30 minutes (2.7GB total reads)
- **BAM Conversion**: 5-10 minutes
- **Sorting**: 5-10 minutes
- **Indexing**: 1-2 minutes
- **Total Time**: 30-50 minutes

## Final Output for IGV
- **Reference**: `MS2_N4.fna`
- **Combined BAM**: `FSEM002C_001O_combined_aligned.sorted.bam`
- **BAM Index**: `FSEM002C_001O_combined_aligned.sorted.bam.bai`
- **Coverage Data**: `combined_coverage_depth.txt`
