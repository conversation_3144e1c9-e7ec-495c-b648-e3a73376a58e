#!/bin/bash

# Optimized BLAST Script for Large FASTA Files
# Senior Bioinformatician Approach: Efficient, Robust, Scalable

set -e  # Exit on any error

echo "=============================================="
echo "Optimized BLAST Analysis Pipeline"
echo "=============================================="

# =============================================================================
# CONFIGURATION
# =============================================================================

BLAST_BIN="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/env/bin/blastn"
INPUT_FILE="${1:-first10.fna}"
OUTPUT_DIR="blast_results"
BATCH_SIZE="${2:-5}"  # Number of sequences per batch
MAX_TARGETS="${3:-5}"
EVALUE="${4:-1e-10}"

# Create output directory
mkdir -p $OUTPUT_DIR

echo "Configuration:"
echo "  Input file: $INPUT_FILE"
echo "  Batch size: $BATCH_SIZE sequences"
echo "  Max targets per query: $MAX_TARGETS"
echo "  E-value threshold: $EVALUE"
echo "  Output directory: $OUTPUT_DIR"
echo ""

# =============================================================================
# FUNCTIONS
# =============================================================================

# Function to split FASTA file into batches
split_fasta() {
    local input_file=$1
    local batch_size=$2
    local output_prefix=$3
    
    echo ">>> Splitting FASTA file into batches of $batch_size sequences..."
    
    awk -v batch_size="$batch_size" -v prefix="$output_prefix" '
    /^>/ {
        if (seq_count % batch_size == 0) {
            if (file_handle) close(file_handle)
            batch_num = int(seq_count / batch_size) + 1
            file_handle = prefix "_batch_" sprintf("%03d", batch_num) ".fna"
        }
        seq_count++
    }
    {
        print > file_handle
    }
    END {
        if (file_handle) close(file_handle)
        print "Created " int((seq_count-1)/batch_size) + 1 " batch files"
    }' "$input_file"
}

# Function to run BLAST on a single batch
run_blast_batch() {
    local batch_file=$1
    local output_file=$2
    
    echo "  Processing: $batch_file"
    
    $BLAST_BIN \
        -query "$batch_file" \
        -db nt \
        -remote \
        -out "$output_file" \
        -outfmt "6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore staxids sscinames" \
        -max_target_seqs $MAX_TARGETS \
        -evalue $EVALUE \
        -num_threads 1
    
    if [ $? -eq 0 ]; then
        echo "  ✓ Completed: $batch_file"
        return 0
    else
        echo "  ✗ Failed: $batch_file"
        return 1
    fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

echo ">>> Analyzing input file..."
TOTAL_SEQS=$(grep -c "^>" "$INPUT_FILE")
echo "Total sequences: $TOTAL_SEQS"

if [ $TOTAL_SEQS -le $BATCH_SIZE ]; then
    echo "File is small enough for single BLAST run."
    echo ""
    echo ">>> Running BLAST on entire file..."
    echo "Start time: $(date)"
    
    OUTPUT_FILE="$OUTPUT_DIR/${INPUT_FILE%.*}_blastn.out"
    
    $BLAST_BIN \
        -query "$INPUT_FILE" \
        -db nt \
        -remote \
        -out "$OUTPUT_FILE" \
        -outfmt "6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore staxids sscinames" \
        -max_target_seqs $MAX_TARGETS \
        -evalue $EVALUE
    
    echo "✓ BLAST completed!"
    echo "Output: $OUTPUT_FILE"
    
else
    echo "Large file detected. Using batch processing approach."
    echo ""
    
    # Split into batches
    split_fasta "$INPUT_FILE" $BATCH_SIZE "$OUTPUT_DIR/$(basename ${INPUT_FILE%.*})"
    
    echo ""
    echo ">>> Running BLAST on batches..."
    echo "Start time: $(date)"
    
    # Process each batch
    BATCH_FILES=($OUTPUT_DIR/*_batch_*.fna)
    TOTAL_BATCHES=${#BATCH_FILES[@]}
    COMPLETED=0
    FAILED=0
    
    for batch_file in "${BATCH_FILES[@]}"; do
        batch_name=$(basename "$batch_file" .fna)
        output_file="$OUTPUT_DIR/${batch_name}_blastn.out"
        
        if run_blast_batch "$batch_file" "$output_file"; then
            ((COMPLETED++))
        else
            ((FAILED++))
        fi
        
        echo "  Progress: $COMPLETED/$TOTAL_BATCHES completed, $FAILED failed"
        
        # Add delay between remote BLAST calls to be respectful to NCBI
        if [ $COMPLETED -lt $TOTAL_BATCHES ]; then
            echo "  Waiting 30 seconds before next batch..."
            sleep 30
        fi
    done
    
    echo ""
    echo ">>> Combining results..."
    COMBINED_OUTPUT="$OUTPUT_DIR/${INPUT_FILE%.*}_combined_blastn.out"
    
    # Combine all batch results
    cat $OUTPUT_DIR/*_batch_*_blastn.out > "$COMBINED_OUTPUT" 2>/dev/null || true
    
    echo "✓ Batch processing completed!"
    echo "Combined output: $COMBINED_OUTPUT"
    
    # Clean up batch files (optional)
    echo ""
    read -p "Remove batch files? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f $OUTPUT_DIR/*_batch_*.fna $OUTPUT_DIR/*_batch_*_blastn.out
        echo "Batch files removed."
    fi
fi

echo ""
echo ">>> Generating summary..."
OUTPUT_FILE="$OUTPUT_DIR/${INPUT_FILE%.*}_blastn.out"
if [ ! -f "$OUTPUT_FILE" ]; then
    OUTPUT_FILE="$OUTPUT_DIR/${INPUT_FILE%.*}_combined_blastn.out"
fi

if [ -f "$OUTPUT_FILE" ]; then
    TOTAL_HITS=$(wc -l < "$OUTPUT_FILE")
    UNIQUE_QUERIES=$(cut -f1 "$OUTPUT_FILE" | sort -u | wc -l)
    
    echo "=== BLAST SUMMARY ==="
    echo "Total hits: $TOTAL_HITS"
    echo "Queries with hits: $UNIQUE_QUERIES/$TOTAL_SEQS"
    echo "Output file: $OUTPUT_FILE"
    echo ""
    
    echo "=== TOP HITS PREVIEW ==="
    echo "Query_ID | Subject_ID | %Identity | Length | E-value | Scientific_Name"
    echo "---------|------------|-----------|--------|---------|----------------"
    head -10 "$OUTPUT_FILE" | awk -F'\t' '{printf "%-8s | %-10s | %8.1f | %6d | %7.0e | %s\n", substr($1,1,8), substr($2,1,10), $3, $4, $11, $14}'
    
    if [ $TOTAL_HITS -gt 10 ]; then
        echo "... (showing first 10 of $TOTAL_HITS hits)"
    fi
else
    echo "No output file found. BLAST may have failed."
fi

echo ""
echo "Completed at: $(date)"
echo "=============================================="
