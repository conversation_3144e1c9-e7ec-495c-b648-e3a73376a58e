#!/bin/bash

# BWA-MEM2 Alignment Script for FSEM001B_S7_L008
# Created for aligning reads to MS2_N4.fna reference genome

# Set paths
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM001B_S7_L008.anqdpht.fastq.gz"
OUTPUT="FSEM001B_S7_L008_aligned.sam"

# Print paths for verification
echo "=== BWA-MEM2 Alignment Configuration ==="
echo "BWA-MEM2 executable: $BWA_MEM2"
echo "Reference genome: $GENOME"
echo "Input reads: $READS"
echo "Output file: $OUTPUT"
echo "========================================="

# Check if files exist
echo "Checking file existence..."
if [ ! -f "$BWA_MEM2" ]; then
    echo "ERROR: BWA-MEM2 executable not found at $BWA_MEM2"
    exit 1
fi

if [ ! -f "$GENOME" ]; then
    echo "ERROR: Reference genome not found at $GENOME"
    exit 1
fi

if [ ! -f "$READS" ]; then
    echo "ERROR: Reads file not found at $READS"
    exit 1
fi

echo "All files found successfully!"

# Check if genome is already indexed
echo "Checking genome index..."
if [ ! -f "${GENOME}.bwt.2bit.64" ]; then
    echo "Genome index not found. Creating index..."
    $BWA_MEM2 index $GENOME
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to index genome"
        exit 1
    fi
    echo "Genome indexing completed successfully!"
else
    echo "Genome index already exists, skipping indexing step."
fi

# Align reads to reference genome
echo "Starting alignment..."
echo "Command: $BWA_MEM2 mem $GENOME $READS > $OUTPUT"
$BWA_MEM2 mem $GENOME $READS > $OUTPUT

# Check if alignment was successful
if [ $? -eq 0 ]; then
    echo "Alignment completed successfully!"
    echo "Output file: $OUTPUT"
    
    # Display basic statistics
    if [ -f "$OUTPUT" ]; then
        echo "=== Alignment Statistics ==="
        echo "Total lines in SAM file: $(wc -l < $OUTPUT)"
        echo "Header lines (starting with @): $(grep -c '^@' $OUTPUT)"
        echo "Alignment lines: $(grep -c -v '^@' $OUTPUT)"
        echo "File size: $(ls -lh $OUTPUT | awk '{print $5}')"
        echo "=========================="
    fi
else
    echo "ERROR: Alignment failed!"
    exit 1
fi

echo "Script completed successfully!"
