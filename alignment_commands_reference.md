# BWA-MEM2 Alignment and BAM Processing Commands Reference

## Quick Start
```bash
# Navigate to working directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV

# Run the complete alignment and BAM processing pipeline
./align_dual_reads.sh
```

## Individual Commands for Manual Execution

### 1. Setup and File Verification
```bash
# Set environment variables
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M"
PIXI_ENV_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"

# Check file sizes
ls -lh $GENOME
ls -lh $READS_DIR/FSEM002C_S40_L007.anqdpht.fastq.gz
ls -lh $READS_DIR/FSEM001O_S20_L008.anqdpht.fastq.gz
```

### 2. BWA-MEM2 Alignment Commands
```bash
# Align FSEM002C_S40_L007 reads
$BWA_MEM2 mem $GENOME $READS_DIR/FSEM002C_S40_L007.anqdpht.fastq.gz > FSEM002C_S40_L007_aligned.sam

# Align FSEM001O_S20_L008 reads
$BWA_MEM2 mem $GENOME $READS_DIR/FSEM001O_S20_L008.anqdpht.fastq.gz > FSEM001O_S20_L008_aligned.sam
```

### 3. SAM to BAM Conversion
```bash
# Navigate to pixi environment
cd $PIXI_ENV_DIR

# Convert SAM to BAM for sample 1
pixi run samtools view -bS ../FSEM002C_S40_L007_aligned.sam > ../FSEM002C_S40_L007_aligned.bam

# Convert SAM to BAM for sample 2
pixi run samtools view -bS ../FSEM001O_S20_L008_aligned.sam > ../FSEM001O_S20_L008_aligned.bam
```

### 4. BAM Sorting
```bash
# Sort BAM files
pixi run samtools sort ../FSEM002C_S40_L007_aligned.bam -o ../FSEM002C_S40_L007_aligned.sorted.bam
pixi run samtools sort ../FSEM001O_S20_L008_aligned.bam -o ../FSEM001O_S20_L008_aligned.sorted.bam
```

### 5. BAM Indexing
```bash
# Create BAM indices for IGV
pixi run samtools index ../FSEM002C_S40_L007_aligned.sorted.bam
pixi run samtools index ../FSEM001O_S20_L008_aligned.sorted.bam
```

### 6. Generate Alignment Statistics
```bash
# Basic statistics
pixi run samtools flagstat ../FSEM002C_S40_L007_aligned.sorted.bam
pixi run samtools flagstat ../FSEM001O_S20_L008_aligned.sorted.bam

# Count mapped/unmapped reads
pixi run samtools view -c -F 4 ../FSEM002C_S40_L007_aligned.sorted.bam  # mapped reads
pixi run samtools view -c -f 4 ../FSEM002C_S40_L007_aligned.sorted.bam  # unmapped reads

pixi run samtools view -c -F 4 ../FSEM001O_S20_L008_aligned.sorted.bam  # mapped reads
pixi run samtools view -c -f 4 ../FSEM001O_S20_L008_aligned.sorted.bam  # unmapped reads
```

### 7. Coverage Analysis
```bash
# Generate coverage depth files
pixi run samtools depth ../FSEM002C_S40_L007_aligned.sorted.bam > ../coverage_FSEM002C.txt
pixi run samtools depth ../FSEM001O_S20_L008_aligned.sorted.bam > ../coverage_FSEM001O.txt

# Generate coverage statistics
pixi run samtools coverage ../FSEM002C_S40_L007_aligned.sorted.bam
pixi run samtools coverage ../FSEM001O_S20_L008_aligned.sorted.bam
```

### 8. Quality Control
```bash
# View first few alignment records
pixi run samtools view ../FSEM002C_S40_L007_aligned.sorted.bam | head -5
pixi run samtools view ../FSEM001O_S20_L008_aligned.sorted.bam | head -5

# Check BAM file integrity
pixi run samtools quickcheck ../FSEM002C_S40_L007_aligned.sorted.bam
pixi run samtools quickcheck ../FSEM001O_S20_L008_aligned.sorted.bam
```

## IGV Viewing Instructions

### Loading Files in IGV:
1. **Load Reference Genome**: 
   - File → Load Genome → Browse to: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna`

2. **Load BAM Files**:
   - File → Load from File → Select:
     - `FSEM002C_S40_L007_aligned.sorted.bam`
     - `FSEM001O_S20_L008_aligned.sorted.bam`
   - The corresponding `.bai` index files will be loaded automatically

3. **Navigate and Analyze**:
   - Use the search box to navigate to specific regions
   - Right-click on tracks for display options
   - View coverage, read alignments, and variants

## File Outputs Summary

### Final IGV-Ready Files:
- **Reference**: `MS2_N4.fna`
- **Sample 1**: `FSEM002C_S40_L007_aligned.sorted.bam` + `.bai`
- **Sample 2**: `FSEM001O_S20_L008_aligned.sorted.bam` + `.bai`

### Intermediate Files:
- **SAM files**: `*_aligned.sam` (large, can be deleted after BAM creation)
- **Unsorted BAM**: `*_aligned.bam` (can be deleted after sorting)
- **Coverage files**: `coverage_*.txt` (for analysis)

## Troubleshooting Commands

### Check if processes are running:
```bash
ps aux | grep bwa-mem2
ps aux | grep samtools
```

### Monitor disk space:
```bash
df -h /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV
```

### Check file integrity:
```bash
file FSEM002C_S40_L007_aligned.sorted.bam
file FSEM001O_S20_L008_aligned.sorted.bam
```

### Clean up intermediate files (optional):
```bash
# Remove large SAM files after BAM creation
rm FSEM002C_S40_L007_aligned.sam
rm FSEM001O_S20_L008_aligned.sam

# Remove unsorted BAM files after sorting
rm FSEM002C_S40_L007_aligned.bam
rm FSEM001O_S20_L008_aligned.bam
```

## Performance Notes
- **FSEM002C_S40_L007**: ~1.7 GB compressed reads
- **FSEM001O_S20_L008**: ~1.0 GB compressed reads
- **Expected runtime**: 15-25 minutes per sample for alignment
- **Expected output size**: 3-8 GB per sorted BAM file
- **Total disk space needed**: ~25-30 GB for all files
