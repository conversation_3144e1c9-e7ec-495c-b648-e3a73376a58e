#!/bin/bash

# Quick Combined BWA-MEM2 Alignment Script for IGV
# Combines two read files into a single sorted BAM for maximum coverage

set -e  # Exit on any error

echo "=============================================="
echo "Quick Combined Alignment for IGV Viewing"
echo "=============================================="

# =============================================================================
# CONFIGURATION
# =============================================================================

BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS1="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM002C_S40_L007.anqdpht.fastq.gz"
READS2="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM001O_S20_L008.anqdpht.fastq.gz"
PIXI_ENV="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"

# Output files
COMBINED_SAM="combined_reads_aligned.sam"
COMBINED_BAM="combined_reads_aligned.bam"
SORTED_BAM="combined_reads_aligned.sorted.bam"
COVERAGE_FILE="combined_coverage_depth.txt"

echo "Input files:"
echo "  Reference: $GENOME ($(ls -lh $GENOME | awk '{print $5}'))"
echo "  Reads 1: $READS1 ($(ls -lh $READS1 | awk '{print $5}'))"
echo "  Reads 2: $READS2 ($(ls -lh $READS2 | awk '{print $5}'))"
echo "  Total read data: $(du -ch $READS1 $READS2 | tail -1 | cut -f1)"
echo ""

# =============================================================================
# STEP 1: ALIGN READS SEPARATELY AND COMBINE
# =============================================================================

echo ">>> STEP 1: Aligning reads separately and combining..."
echo "Start time: $(date)"

echo "Aligning first read file (FSEM002C)..."
$BWA_MEM2 mem $GENOME $READS1 > temp1.sam

echo "Aligning second read file (FSEM001O)..."
$BWA_MEM2 mem $GENOME $READS2 > temp2.sam

echo "Combining SAM files..."
# Extract header from first file
grep '^@' temp1.sam > $COMBINED_SAM
# Add all alignment records (no headers)
grep -v '^@' temp1.sam >> $COMBINED_SAM
grep -v '^@' temp2.sam >> $COMBINED_SAM

# Clean up temporary files
rm temp1.sam temp2.sam

echo "✓ Combined alignment completed!"
echo "  Output: $COMBINED_SAM ($(ls -lh $COMBINED_SAM | awk '{print $5}'))"

# =============================================================================
# STEP 2: CONVERT TO BAM
# =============================================================================

echo ""
echo ">>> STEP 2: Converting SAM to BAM..."
cd $PIXI_ENV
pixi run samtools view -bS ../$COMBINED_SAM > ../$COMBINED_BAM
cd ..

echo "✓ BAM conversion completed!"
echo "  Output: $COMBINED_BAM ($(ls -lh $COMBINED_BAM | awk '{print $5}'))"

# =============================================================================
# STEP 3: SORT BAM
# =============================================================================

echo ""
echo ">>> STEP 3: Sorting BAM file..."
cd $PIXI_ENV
pixi run samtools sort ../$COMBINED_BAM -o ../$SORTED_BAM
cd ..

echo "✓ BAM sorting completed!"
echo "  Output: $SORTED_BAM ($(ls -lh $SORTED_BAM | awk '{print $5}'))"

# =============================================================================
# STEP 4: INDEX BAM FOR IGV
# =============================================================================

echo ""
echo ">>> STEP 4: Creating BAM index for IGV..."
cd $PIXI_ENV
pixi run samtools index ../$SORTED_BAM
cd ..

echo "✓ BAM indexing completed!"
echo "  Index: ${SORTED_BAM}.bai"

# =============================================================================
# STEP 5: GENERATE STATISTICS AND COVERAGE
# =============================================================================

echo ""
echo ">>> STEP 5: Generating alignment statistics..."
cd $PIXI_ENV

echo ""
echo "=== ALIGNMENT STATISTICS ==="
pixi run samtools flagstat ../$SORTED_BAM

echo ""
echo "=== COVERAGE SUMMARY ==="
pixi run samtools coverage ../$SORTED_BAM

echo ""
echo "=== READ COUNTS ==="
TOTAL_READS=$(pixi run samtools view -c ../$SORTED_BAM)
MAPPED_READS=$(pixi run samtools view -c -F 4 ../$SORTED_BAM)
UNMAPPED_READS=$(pixi run samtools view -c -f 4 ../$SORTED_BAM)

echo "Total reads processed: $TOTAL_READS"
echo "Mapped reads: $MAPPED_READS"
echo "Unmapped reads: $UNMAPPED_READS"

if [ $TOTAL_READS -gt 0 ]; then
    MAPPING_RATE=$(echo "scale=2; $MAPPED_READS * 100 / $TOTAL_READS" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.2f\", $MAPPED_READS * 100 / $TOTAL_READS}")
    echo "Mapping rate: ${MAPPING_RATE}%"
fi

echo ""
echo ">>> Generating detailed coverage depth file..."
pixi run samtools depth ../$SORTED_BAM > ../$COVERAGE_FILE
cd ..

echo "✓ Coverage analysis completed!"
echo "  Coverage file: $COVERAGE_FILE ($(wc -l < $COVERAGE_FILE) positions)"

# =============================================================================
# STEP 6: CLEANUP AND FINAL SUMMARY
# =============================================================================

echo ""
echo ">>> STEP 6: Cleaning up intermediate files..."
rm $COMBINED_SAM $COMBINED_BAM

echo ""
echo "=============================================="
echo "✅ ALIGNMENT PIPELINE COMPLETED SUCCESSFULLY!"
echo "=============================================="
echo ""
echo "📁 IGV-Ready Files Created:"
echo "   Reference Genome: $GENOME"
echo "   Sorted BAM: $SORTED_BAM"
echo "   BAM Index: ${SORTED_BAM}.bai"
echo "   Coverage Data: $COVERAGE_FILE"
echo ""
echo "📊 Final File Sizes:"
echo "   Sorted BAM: $(ls -lh $SORTED_BAM | awk '{print $5}')"
echo "   Coverage file: $(ls -lh $COVERAGE_FILE | awk '{print $5}')"
echo ""
echo "🔬 To Load in IGV:"
echo "   1. File → Load Genome → $GENOME"
echo "   2. File → Load from File → $SORTED_BAM"
echo "   3. The .bai index will be automatically detected"
echo ""
echo "📈 Read Sources Combined:"
echo "   - FSEM002C_S40_L007.anqdpht.fastq.gz"
echo "   - FSEM001O_S20_L008.anqdpht.fastq.gz"
echo ""
echo "🎯 Result: Single BAM with maximum coverage from both samples"
echo ""
echo "Completed at: $(date)"
echo "=============================================="
