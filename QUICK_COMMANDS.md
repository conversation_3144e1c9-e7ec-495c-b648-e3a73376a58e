# Quick Combined Alignment Commands for IGV

## 🚀 ONE-COMMAND SOLUTION

```bash
# Navigate to working directory and run complete pipeline
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV
./quick_combined_alignment.sh
```

**This single script does everything:**
- Aligns both read files separately (avoids paired-end conflicts)
- Combines them into one SAM file
- Converts to BAM
- Sorts the BAM
- Creates index for IGV
- Generates coverage statistics
- Cleans up intermediate files

---

## 📋 Manual Commands (if needed)

### Step-by-Step Manual Execution:
```bash
# Set variables
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS1="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM002C_S40_L007.anqdpht.fastq.gz"
READS2="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M/FSEM001O_S20_L008.anqdpht.fastq.gz"

# 1. Align reads separately
$BWA_MEM2 mem $GENOME $READS1 > temp1.sam
$BWA_MEM2 mem $GENOME $READS2 > temp2.sam

# 2. Combine SAM files
grep '^@' temp1.sam > combined_reads_aligned.sam
grep -v '^@' temp1.sam >> combined_reads_aligned.sam
grep -v '^@' temp2.sam >> combined_reads_aligned.sam
rm temp1.sam temp2.sam

# 3. Convert to BAM and sort
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env
pixi run samtools view -bS ../combined_reads_aligned.sam > ../combined_reads_aligned.bam
pixi run samtools sort ../combined_reads_aligned.bam -o ../combined_reads_aligned.sorted.bam
pixi run samtools index ../combined_reads_aligned.sorted.bam
cd ..

# 4. Generate coverage
cd bwa-env
pixi run samtools depth ../combined_reads_aligned.sorted.bam > ../combined_coverage_depth.txt
cd ..
```

---

## 📁 Expected Output Files

After running the script, you'll have these IGV-ready files:

```
/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/
├── MS2_N4.fna                           # Reference genome
├── combined_reads_aligned.sorted.bam    # Main BAM file for IGV
├── combined_reads_aligned.sorted.bam.bai # Index file (auto-detected by IGV)
└── combined_coverage_depth.txt          # Coverage analysis
```

---

## 🔬 Loading in IGV

### Method 1: IGV Desktop Application
1. **Load Genome**: File → Load Genome → Browse to `MS2_N4.fna`
2. **Load BAM**: File → Load from File → Select `combined_reads_aligned.sorted.bam`
3. **Navigate**: Use search box or scroll to view alignments

### Method 2: IGV Web (if available)
1. Upload reference genome file
2. Upload BAM and BAI files
3. View alignments and coverage

---

## 📊 Quick Statistics Commands

```bash
cd /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env

# Basic alignment stats
pixi run samtools flagstat ../combined_reads_aligned.sorted.bam

# Coverage summary
pixi run samtools coverage ../combined_reads_aligned.sorted.bam

# Count reads
pixi run samtools view -c ../combined_reads_aligned.sorted.bam                    # Total reads
pixi run samtools view -c -F 4 ../combined_reads_aligned.sorted.bam              # Mapped reads
pixi run samtools view -c -f 4 ../combined_reads_aligned.sorted.bam              # Unmapped reads
```

---

## 🎯 What This Achieves

**Input**: 
- FSEM002C_S40_L007.anqdpht.fastq.gz (1.7GB)
- FSEM001O_S20_L008.anqdpht.fastq.gz (1.0GB)
- Total: 2.7GB of read data

**Output**: 
- Single sorted BAM with ALL reads from both files
- Maximum possible coverage on MS2_N4 reference
- Ready for IGV visualization and analysis

**Benefits**:
- ✅ Combines reads from both samples for maximum coverage
- ✅ Avoids paired-end read name conflicts
- ✅ Single BAM file (easier to manage)
- ✅ Optimized for variant detection and coverage analysis
- ✅ IGV-ready with automatic index detection

---

## ⏱️ Expected Runtime
- **Total time**: 30-45 minutes
- **Alignment**: ~25-35 minutes (both files)
- **BAM processing**: ~5-10 minutes
- **Final output**: ~3-6GB sorted BAM file

---

## 🔧 Troubleshooting

### Check if script is running:
```bash
ps aux | grep bwa-mem2
ps aux | grep samtools
```

### Monitor progress:
```bash
# Watch file sizes grow
watch -n 30 'ls -lh /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/*.sam /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/*.bam 2>/dev/null'
```

### Check disk space:
```bash
df -h /clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV
```

---

## 🧹 Cleanup (Optional)
```bash
# Remove intermediate files to save space (only after confirming BAM works)
rm combined_reads_aligned.sam
rm combined_reads_aligned.bam
# Keep: combined_reads_aligned.sorted.bam and .bai
```
