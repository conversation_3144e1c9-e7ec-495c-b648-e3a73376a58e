#!/bin/bash

# Comprehensive BWA-MEM2 Alignment and BAM Processing Script
# Aligns two read files to MS2_N4.fna reference genome and creates sorted BAM files for IGV

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

# Set paths
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M"
PIXI_ENV_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"

# Read files
READS1="${READS_DIR}/FSEM002C_S40_L007.anqdpht.fastq.gz"
READS2="${READS_DIR}/FSEM001O_S20_L008.anqdpht.fastq.gz"

# Output files
SAM1="FSEM002C_S40_L007_aligned.sam"
SAM2="FSEM001O_S20_L008_aligned.sam"
BAM1="FSEM002C_S40_L007_aligned.bam"
BAM2="FSEM001O_S20_L008_aligned.bam"
SORTED_BAM1="FSEM002C_S40_L007_aligned.sorted.bam"
SORTED_BAM2="FSEM001O_S20_L008_aligned.sorted.bam"

# =============================================================================
# FUNCTIONS
# =============================================================================

print_header() {
    echo "=============================================="
    echo "$1"
    echo "=============================================="
}

print_step() {
    echo ""
    echo ">>> $1"
    echo ""
}

check_file() {
    if [ ! -f "$1" ]; then
        echo "ERROR: File not found: $1"
        exit 1
    fi
    echo "✓ Found: $1"
}

get_file_size() {
    ls -lh "$1" | awk '{print $5}'
}

# =============================================================================
# MAIN SCRIPT
# =============================================================================

print_header "BWA-MEM2 Dual Read Alignment and BAM Processing"

echo "Configuration:"
echo "  BWA-MEM2: $BWA_MEM2"
echo "  Reference: $GENOME"
echo "  Read 1: $READS1"
echo "  Read 2: $READS2"
echo "  Working directory: $(pwd)"
echo ""

# Check if all required files exist
print_step "Checking input files..."
check_file "$BWA_MEM2"
check_file "$GENOME"
check_file "$READS1"
check_file "$READS2"

echo ""
echo "Input file sizes:"
echo "  Reference genome: $(get_file_size $GENOME)"
echo "  Read file 1: $(get_file_size $READS1)"
echo "  Read file 2: $(get_file_size $READS2)"

# Check if genome is indexed
print_step "Checking genome index..."
if [ ! -f "${GENOME}.bwt.2bit.64" ]; then
    echo "Genome index not found. Creating index..."
    $BWA_MEM2 index $GENOME
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to index genome"
        exit 1
    fi
    echo "✓ Genome indexing completed!"
else
    echo "✓ Genome index already exists"
fi

# =============================================================================
# ALIGNMENT 1: FSEM002C_S40_L007
# =============================================================================

print_header "ALIGNMENT 1: FSEM002C_S40_L007"

print_step "Starting BWA-MEM2 alignment for FSEM002C_S40_L007..."
echo "Command: $BWA_MEM2 mem $GENOME $READS1 > $SAM1"
echo "Start time: $(date)"

$BWA_MEM2 mem $GENOME $READS1 > $SAM1

if [ $? -eq 0 ]; then
    echo "✓ Alignment 1 completed successfully!"
    echo "  Output: $SAM1"
    echo "  Size: $(get_file_size $SAM1)"
    echo "  End time: $(date)"
else
    echo "ERROR: Alignment 1 failed!"
    exit 1
fi

# =============================================================================
# ALIGNMENT 2: FSEM001O_S20_L008
# =============================================================================

print_header "ALIGNMENT 2: FSEM001O_S20_L008"

print_step "Starting BWA-MEM2 alignment for FSEM001O_S20_L008..."
echo "Command: $BWA_MEM2 mem $GENOME $READS2 > $SAM2"
echo "Start time: $(date)"

$BWA_MEM2 mem $GENOME $READS2 > $SAM2

if [ $? -eq 0 ]; then
    echo "✓ Alignment 2 completed successfully!"
    echo "  Output: $SAM2"
    echo "  Size: $(get_file_size $SAM2)"
    echo "  End time: $(date)"
else
    echo "ERROR: Alignment 2 failed!"
    exit 1
fi

# =============================================================================
# BAM CONVERSION AND PROCESSING
# =============================================================================

print_header "BAM CONVERSION AND PROCESSING"

# Navigate to pixi environment directory for samtools commands
cd "$PIXI_ENV_DIR"

# Process SAM1 -> BAM1 -> SORTED_BAM1
print_step "Converting $SAM1 to BAM format..."
echo "Command: pixi run samtools view -bS ../$SAM1 > ../$BAM1"
pixi run samtools view -bS "../$SAM1" > "../$BAM1"

if [ $? -eq 0 ]; then
    echo "✓ SAM to BAM conversion completed for file 1!"
    echo "  Output: $BAM1"
    echo "  Size: $(get_file_size ../$BAM1)"
else
    echo "ERROR: SAM to BAM conversion failed for file 1!"
    exit 1
fi

print_step "Sorting $BAM1..."
echo "Command: pixi run samtools sort ../$BAM1 -o ../$SORTED_BAM1"
pixi run samtools sort "../$BAM1" -o "../$SORTED_BAM1"

if [ $? -eq 0 ]; then
    echo "✓ BAM sorting completed for file 1!"
    echo "  Output: $SORTED_BAM1"
    echo "  Size: $(get_file_size ../$SORTED_BAM1)"
else
    echo "ERROR: BAM sorting failed for file 1!"
    exit 1
fi

print_step "Indexing $SORTED_BAM1..."
echo "Command: pixi run samtools index ../$SORTED_BAM1"
pixi run samtools index "../$SORTED_BAM1"

if [ $? -eq 0 ]; then
    echo "✓ BAM indexing completed for file 1!"
    echo "  Index file: ${SORTED_BAM1}.bai"
else
    echo "ERROR: BAM indexing failed for file 1!"
    exit 1
fi

# Process SAM2 -> BAM2 -> SORTED_BAM2
print_step "Converting $SAM2 to BAM format..."
echo "Command: pixi run samtools view -bS ../$SAM2 > ../$BAM2"
pixi run samtools view -bS "../$SAM2" > "../$BAM2"

if [ $? -eq 0 ]; then
    echo "✓ SAM to BAM conversion completed for file 2!"
    echo "  Output: $BAM2"
    echo "  Size: $(get_file_size ../$BAM2)"
else
    echo "ERROR: SAM to BAM conversion failed for file 2!"
    exit 1
fi

print_step "Sorting $BAM2..."
echo "Command: pixi run samtools sort ../$BAM2 -o ../$SORTED_BAM2"
pixi run samtools sort "../$BAM2" -o "../$SORTED_BAM2"

if [ $? -eq 0 ]; then
    echo "✓ BAM sorting completed for file 2!"
    echo "  Output: $SORTED_BAM2"
    echo "  Size: $(get_file_size ../$SORTED_BAM2)"
else
    echo "ERROR: BAM sorting failed for file 2!"
    exit 1
fi

print_step "Indexing $SORTED_BAM2..."
echo "Command: pixi run samtools index ../$SORTED_BAM2"
pixi run samtools index "../$SORTED_BAM2"

if [ $? -eq 0 ]; then
    echo "✓ BAM indexing completed for file 2!"
    echo "  Index file: ${SORTED_BAM2}.bai"
else
    echo "ERROR: BAM indexing failed for file 2!"
    exit 1
fi

# =============================================================================
# GENERATE ALIGNMENT STATISTICS
# =============================================================================

print_header "GENERATING ALIGNMENT STATISTICS"

cd ..  # Go back to main directory

print_step "Generating alignment statistics for both files..."

echo "=== ALIGNMENT STATISTICS FOR FSEM002C_S40_L007 ==="
echo "SAM file: $SAM1"
echo "Total lines: $(wc -l < $SAM1)"
echo "Header lines: $(grep -c '^@' $SAM1)"
echo "Alignment records: $(grep -c -v '^@' $SAM1)"
echo "Mapped reads: $(cd $PIXI_ENV_DIR && pixi run samtools view -c -F 4 ../$SORTED_BAM1)"
echo "Unmapped reads: $(cd $PIXI_ENV_DIR && pixi run samtools view -c -f 4 ../$SORTED_BAM1)"

echo ""
echo "=== ALIGNMENT STATISTICS FOR FSEM001O_S20_L008 ==="
echo "SAM file: $SAM2"
echo "Total lines: $(wc -l < $SAM2)"
echo "Header lines: $(grep -c '^@' $SAM2)"
echo "Alignment records: $(grep -c -v '^@' $SAM2)"
echo "Mapped reads: $(cd $PIXI_ENV_DIR && pixi run samtools view -c -F 4 ../$SORTED_BAM2)"
echo "Unmapped reads: $(cd $PIXI_ENV_DIR && pixi run samtools view -c -f 4 ../$SORTED_BAM2)"

# =============================================================================
# FINAL SUMMARY
# =============================================================================

print_header "PROCESSING COMPLETE - SUMMARY"

echo "✅ All alignments and BAM processing completed successfully!"
echo ""
echo "📁 Output Files for IGV Viewing:"
echo "   Reference Genome: $GENOME"
echo ""
echo "   Sample 1 (FSEM002C_S40_L007):"
echo "     - Sorted BAM: $SORTED_BAM1"
echo "     - BAM Index: ${SORTED_BAM1}.bai"
echo "     - Original SAM: $SAM1"
echo "     - Size: $(get_file_size $SORTED_BAM1)"
echo ""
echo "   Sample 2 (FSEM001O_S20_L008):"
echo "     - Sorted BAM: $SORTED_BAM2"
echo "     - BAM Index: ${SORTED_BAM2}.bai"
echo "     - Original SAM: $SAM2"
echo "     - Size: $(get_file_size $SORTED_BAM2)"
echo ""
echo "🔬 To view in IGV:"
echo "   1. Load reference genome: $GENOME"
echo "   2. Load BAM files: $SORTED_BAM1 and $SORTED_BAM2"
echo "   3. The .bai index files will be automatically detected"
echo ""
echo "📊 Coverage analysis can be performed using:"
echo "   cd $PIXI_ENV_DIR"
echo "   pixi run samtools depth ../$SORTED_BAM1 > ../coverage_FSEM002C.txt"
echo "   pixi run samtools depth ../$SORTED_BAM2 > ../coverage_FSEM001O.txt"
echo ""
echo "Script completed at: $(date)"
