#!/bin/bash

# Combined BWA-MEM2 Alignment Script
# Aligns two read files together to MS2_N4.fna reference genome and creates a single merged BAM file

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

# Set paths
BWA_MEM2="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/.pixi/envs/default/bin/bwa-mem2"
GENOME="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/MS2_N4.fna"
READS_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/00data/readsf_10M"
PIXI_ENV_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env"

# Read files
READS1="${READS_DIR}/FSEM002C_S40_L007.anqdpht.fastq.gz"
READS2="${READS_DIR}/FSEM001O_S20_L008.anqdpht.fastq.gz"

# Output files
COMBINED_SAM="FSEM002C_001O_combined_aligned.sam"
COMBINED_BAM="FSEM002C_001O_combined_aligned.bam"
SORTED_BAM="FSEM002C_001O_combined_aligned.sorted.bam"

# =============================================================================
# FUNCTIONS
# =============================================================================

print_header() {
    echo "=============================================="
    echo "$1"
    echo "=============================================="
}

print_step() {
    echo ""
    echo ">>> $1"
    echo ""
}

check_file() {
    if [ ! -f "$1" ]; then
        echo "ERROR: File not found: $1"
        exit 1
    fi
    echo "✓ Found: $1"
}

get_file_size() {
    ls -lh "$1" | awk '{print $5}'
}

# =============================================================================
# MAIN SCRIPT
# =============================================================================

print_header "BWA-MEM2 Combined Read Alignment to Single BAM"

echo "Configuration:"
echo "  BWA-MEM2: $BWA_MEM2"
echo "  Reference: $GENOME"
echo "  Read file 1: $READS1"
echo "  Read file 2: $READS2"
echo "  Combined output: $SORTED_BAM"
echo "  Working directory: $(pwd)"
echo ""

# Check if all required files exist
print_step "Checking input files..."
check_file "$BWA_MEM2"
check_file "$GENOME"
check_file "$READS1"
check_file "$READS2"

echo ""
echo "Input file sizes:"
echo "  Reference genome: $(get_file_size $GENOME)"
echo "  Read file 1: $(get_file_size $READS1)"
echo "  Read file 2: $(get_file_size $READS2)"
echo "  Total read data: $(du -ch $READS1 $READS2 | tail -1 | cut -f1)"

# Check if genome is indexed
print_step "Checking genome index..."
if [ ! -f "${GENOME}.bwt.2bit.64" ]; then
    echo "Genome index not found. Creating index..."
    $BWA_MEM2 index $GENOME
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to index genome"
        exit 1
    fi
    echo "✓ Genome indexing completed!"
else
    echo "✓ Genome index already exists"
fi

# =============================================================================
# COMBINED ALIGNMENT
# =============================================================================

print_header "COMBINED ALIGNMENT: BOTH READ FILES TO SINGLE SAM"

print_step "Starting BWA-MEM2 alignment with combined reads..."
echo "Strategy: Aligning each file separately, then merging SAM files for maximum coverage"
echo "Commands: "
echo "  $BWA_MEM2 mem $GENOME $READS1 > temp_sam1.sam"
echo "  $BWA_MEM2 mem $GENOME $READS2 > temp_sam2.sam"
echo "  Merge SAM files > $COMBINED_SAM"
echo "Start time: $(date)"

# Align each read file separately, then combine SAM files
echo "Aligning first read file..."
$BWA_MEM2 mem $GENOME $READS1 > temp_sam1.sam

echo "Aligning second read file..."
$BWA_MEM2 mem $GENOME $READS2 > temp_sam2.sam

echo "Combining SAM files..."
# Extract header from first file
grep '^@' temp_sam1.sam > $COMBINED_SAM
# Add alignment records from both files (skip headers)
grep -v '^@' temp_sam1.sam >> $COMBINED_SAM
grep -v '^@' temp_sam2.sam >> $COMBINED_SAM

# Clean up temporary files
rm temp_sam1.sam temp_sam2.sam

if [ $? -eq 0 ]; then
    echo "✓ Combined alignment completed successfully!"
    echo "  Output: $COMBINED_SAM"
    echo "  Size: $(get_file_size $COMBINED_SAM)"
    echo "  End time: $(date)"
else
    echo "ERROR: Combined alignment failed!"
    exit 1
fi

# =============================================================================
# BAM CONVERSION AND PROCESSING
# =============================================================================

print_header "BAM CONVERSION AND PROCESSING"

# Navigate to pixi environment directory for samtools commands
cd "$PIXI_ENV_DIR"

print_step "Converting combined SAM to BAM format..."
echo "Command: pixi run samtools view -bS ../$COMBINED_SAM > ../$COMBINED_BAM"
pixi run samtools view -bS "../$COMBINED_SAM" > "../$COMBINED_BAM"

if [ $? -eq 0 ]; then
    echo "✓ SAM to BAM conversion completed!"
    echo "  Output: $COMBINED_BAM"
    echo "  Size: $(get_file_size ../$COMBINED_BAM)"
else
    echo "ERROR: SAM to BAM conversion failed!"
    exit 1
fi

print_step "Sorting combined BAM file..."
echo "Command: pixi run samtools sort ../$COMBINED_BAM -o ../$SORTED_BAM"
pixi run samtools sort "../$COMBINED_BAM" -o "../$SORTED_BAM"

if [ $? -eq 0 ]; then
    echo "✓ BAM sorting completed!"
    echo "  Output: $SORTED_BAM"
    echo "  Size: $(get_file_size ../$SORTED_BAM)"
else
    echo "ERROR: BAM sorting failed!"
    exit 1
fi

print_step "Indexing sorted BAM file..."
echo "Command: pixi run samtools index ../$SORTED_BAM"
pixi run samtools index "../$SORTED_BAM"

if [ $? -eq 0 ]; then
    echo "✓ BAM indexing completed!"
    echo "  Index file: ${SORTED_BAM}.bai"
else
    echo "ERROR: BAM indexing failed!"
    exit 1
fi

# =============================================================================
# GENERATE COMPREHENSIVE STATISTICS
# =============================================================================

print_header "GENERATING ALIGNMENT STATISTICS"

cd ..  # Go back to main directory

print_step "Analyzing combined alignment results..."

echo "=== COMBINED ALIGNMENT STATISTICS ==="
echo "SAM file: $COMBINED_SAM"
echo "Total lines in SAM: $(wc -l < $COMBINED_SAM)"
echo "Header lines: $(grep -c '^@' $COMBINED_SAM)"
echo "Total alignment records: $(grep -c -v '^@' $COMBINED_SAM)"

# Detailed samtools statistics
cd "$PIXI_ENV_DIR"
echo ""
echo "=== DETAILED BAM STATISTICS ==="
pixi run samtools flagstat "../$SORTED_BAM"

echo ""
echo "=== READ MAPPING SUMMARY ==="
TOTAL_READS=$(pixi run samtools view -c "../$SORTED_BAM")
MAPPED_READS=$(pixi run samtools view -c -F 4 "../$SORTED_BAM")
UNMAPPED_READS=$(pixi run samtools view -c -f 4 "../$SORTED_BAM")
MAPPING_RATE=$(echo "scale=2; $MAPPED_READS * 100 / $TOTAL_READS" | bc -l)

echo "Total reads processed: $TOTAL_READS"
echo "Mapped reads: $MAPPED_READS"
echo "Unmapped reads: $UNMAPPED_READS"
echo "Mapping rate: ${MAPPING_RATE}%"

echo ""
echo "=== COVERAGE ANALYSIS ==="
pixi run samtools coverage "../$SORTED_BAM"

cd ..

# =============================================================================
# FINAL SUMMARY
# =============================================================================

print_header "PROCESSING COMPLETE - COMBINED BAM READY FOR IGV"

echo "✅ Combined alignment and BAM processing completed successfully!"
echo ""
echo "📁 Final Output Files for IGV Viewing:"
echo "   Reference Genome: $GENOME"
echo "   Combined Sorted BAM: $SORTED_BAM"
echo "   BAM Index: ${SORTED_BAM}.bai"
echo "   File Size: $(get_file_size $SORTED_BAM)"
echo ""
echo "📊 Read Sources Combined:"
echo "   - FSEM002C_S40_L007.anqdpht.fastq.gz ($(get_file_size $READS1))"
echo "   - FSEM001O_S20_L008.anqdpht.fastq.gz ($(get_file_size $READS2))"
echo ""
echo "🔬 To view in IGV:"
echo "   1. Load reference genome: $GENOME"
echo "   2. Load BAM file: $SORTED_BAM"
echo "   3. The .bai index file will be automatically detected"
echo ""
echo "📈 Coverage depth analysis:"
echo "   cd $PIXI_ENV_DIR"
echo "   pixi run samtools depth ../$SORTED_BAM > ../combined_coverage_depth.txt"
echo ""
echo "🧹 Optional cleanup (save disk space):"
echo "   rm $COMBINED_SAM  # Remove large SAM file"
echo "   rm $COMBINED_BAM  # Remove unsorted BAM file"
echo ""
echo "Script completed at: $(date)"
